<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>Soup</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <meta name="description" content="Description" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, minimum-scale=1.0"
    />
    <link
      rel="stylesheet"
      href="//cdn.jsdelivr.net/npm/docsify@4/lib/themes/vue.css"
    />
    <link rel="icon" type="image/x-icon" href="/favicon.ico">

    <style>
      :root {
        --theme-color: #df8c48;
        --sidebar-nav-link-color--active: #df8c48;
      }
    </style>

    <!-- Table of Contents styles -->
    <link
      rel="stylesheet"
      href="https://unpkg.com/docsify-plugin-toc@1.3.1/dist/light.css"
    >
  </head>

  <body>
    <div id="app"></div>
    <script>
      window.$docsify = {
        name: "🥣 Soup",
        repo: "https://github.com/Sov3rain/soup",
        loadSidebar: true,
        toc: {
          tocMaxLevel: 5,
          target: "h2, h3, h4, h5, h6",
        },
      };
    </script>

    <!-- Docsify v4 -->
    <script src="//cdn.jsdelivr.net/npm/docsify@4"></script>

    <!-- Pagination plugin -->
    <script
      src="//unpkg.com/docsify-pagination/dist/docsify-pagination.min.js"
    ></script>

    <!-- Copy code plugin -->
    <script src="https://unpkg.com/docsify-copy-code@3"></script>

    <!-- Table of Contents plugin -->
    <script
      src="https://unpkg.com/docsify-plugin-toc@1.3.1/dist/docsify-plugin-toc.min.js"
    ></script>

    <!-- Search plugin -->
    <script
      src="//cdn.jsdelivr.net/npm/docsify/lib/plugins/search.min.js"
    ></script>

    <!-- Prism.js for C# syntax highlighting -->
    <script src="//unpkg.com/prismjs/components/prism-csharp.min.js"></script>

    <!-- Prism.js for JSON syntax highlighting -->
    <script src="//unpkg.com/prismjs/components/prism-json.min.js"></script>
  </body>
</html>
